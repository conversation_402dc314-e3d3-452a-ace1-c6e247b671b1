export default function Hero() {
  return (
    <section className="relative pt-20 pb-16 lg:pt-32 lg:pb-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          {/* Main Heading */}
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground mb-6 animate-fade-in-up">
            The complete office suite
            <span className="block text-primary">for modern teams</span>
          </h1>

          {/* Subtitle */}
          <p className="text-lg sm:text-xl text-muted max-w-3xl mx-auto mb-8 animate-fade-in-up" style={{ animationDelay: '0.1s' }}>
            Appfice delivers enterprise-grade productivity tools with seamless collaboration,
            advanced security, and intelligent automation to power your organization's success.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            <button className="btn btn-primary btn-lg">
              Start free trial
            </button>
            <button className="btn btn-secondary btn-lg">
              Schedule demo
            </button>
          </div>

          {/* Trust Indicators */}
          <div className="animate-fade-in-up" style={{ animationDelay: '0.3s' }}>
            <p className="text-sm text-muted mb-6">Trusted by over 50,000 organizations worldwide</p>
            <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
              {/* Company logos would go here in a real implementation */}
              <div className="text-2xl font-bold text-muted">Microsoft</div>
              <div className="text-2xl font-bold text-muted">Google</div>
              <div className="text-2xl font-bold text-muted">Amazon</div>
              <div className="text-2xl font-bold text-muted">Apple</div>
              <div className="text-2xl font-bold text-muted">Meta</div>
            </div>
          </div>
        </div>
      </div>

      {/* Product Preview */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 mt-16">
        <div className="card card-elevated p-4 animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
          <div className="aspect-video bg-gradient-to-br from-primary/10 to-accent/10 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2-10h.01M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <p className="text-muted">Interactive product demo</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
