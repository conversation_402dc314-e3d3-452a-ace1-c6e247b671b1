'use client';

import { useState } from 'react';

const plans = [
  {
    name: 'Business',
    price: { monthly: 12, yearly: 120 },
    description: 'For growing teams and small businesses',
    features: [
      'All productivity applications',
      '1TB cloud storage per user',
      'Real-time collaboration',
      'Email and chat support',
      'Mobile and desktop apps',
      'Basic admin controls'
    ],
    popular: false
  },
  {
    name: 'Enterprise',
    price: { monthly: 22, yearly: 220 },
    description: 'For large organizations with advanced needs',
    features: [
      'Everything in Business',
      'Unlimited cloud storage',
      'Advanced security controls',
      'SSO and directory integration',
      'Priority support',
      'Advanced analytics',
      'Custom integrations',
      'Compliance tools'
    ],
    popular: true
  },
  {
    name: 'Enterprise Plus',
    price: { monthly: 35, yearly: 350 },
    description: 'For mission-critical deployments',
    features: [
      'Everything in Enterprise',
      'Dedicated customer success manager',
      '99.9% uptime SLA',
      'Advanced threat protection',
      'Custom development support',
      'On-premises deployment options',
      'White-label solutions',
      '24/7 phone support'
    ],
    popular: false
  }
];

export default function Pricing() {
  const [isYearly, setIsYearly] = useState(false);

  return (
    <section id="pricing" className="py-16 lg:py-24 bg-surface">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-light text-foreground mb-6">
            Find the right <span className="font-semibold">plan</span> for you
          </h2>
          <p className="text-lg text-muted max-w-3xl mx-auto mb-8">
            Get the apps you need to be productive at work, school, or home. All plans include a free trial.
          </p>

          {/* Billing Toggle */}
          <div className="inline-flex items-center bg-card rounded-lg p-1 shadow-sm">
            <button
              onClick={() => setIsYearly(false)}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
                !isYearly
                  ? 'bg-primary text-white shadow-sm'
                  : 'text-muted hover:text-foreground'
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setIsYearly(true)}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
                isYearly
                  ? 'bg-primary text-white shadow-sm'
                  : 'text-muted hover:text-foreground'
              }`}
            >
              Annual
              <span className="ml-1 text-xs bg-white/20 px-1.5 py-0.5 rounded">
                Save 17%
              </span>
            </button>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto mb-16">
          {plans.map((plan, index) => (
            <div
              key={plan.name}
              className={`bg-card rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 relative ${
                plan.popular ? 'ring-2 ring-primary transform scale-105' : ''
              }`}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-primary text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
                    Most popular
                  </div>
                </div>
              )}

              <div className="p-8">
                {/* Plan Header */}
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-semibold text-foreground mb-2">
                    Appfice {plan.name}
                  </h3>
                  <p className="text-muted text-sm mb-6">
                    {plan.description}
                  </p>

                  {/* Price */}
                  <div className="mb-6">
                    <div className="flex items-baseline justify-center">
                      <span className="text-5xl font-light text-foreground">
                        ${isYearly ? plan.price.yearly : plan.price.monthly}
                      </span>
                      <span className="text-muted ml-2 text-lg">
                        /{isYearly ? 'year' : 'month'}
                      </span>
                    </div>
                    {isYearly && (
                      <div className="text-sm text-muted mt-2">
                        ${(plan.price.yearly / 12).toFixed(0)}/month billed annually
                      </div>
                    )}
                  </div>
                </div>

                {/* CTA Button */}
                <div className="mb-8">
                  <button className={`w-full py-3 px-6 rounded-md font-semibold transition-all duration-200 ${
                    plan.popular
                      ? 'btn btn-primary btn-lg'
                      : 'btn btn-outline btn-lg'
                  }`}>
                    {plan.name === 'Enterprise Plus' ? 'Contact sales' : 'Try free for 1 month'}
                  </button>
                </div>

                {/* Features */}
                <div className="space-y-4">
                  <h4 className="text-sm font-semibold text-foreground uppercase tracking-wide">
                    What's included
                  </h4>
                  <ul className="space-y-3">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={feature} className="flex items-start">
                        <svg className="w-5 h-5 text-primary mr-3 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        <span className="text-muted text-sm leading-relaxed">
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom Section */}
        <div className="bg-card rounded-lg shadow-lg p-8 lg:p-12 text-center">
          <h3 className="text-2xl lg:text-3xl font-light text-foreground mb-4">
            Questions about <span className="font-semibold">Appfice</span>?
          </h3>
          <p className="text-muted mb-8 max-w-2xl mx-auto">
            Our team is here to help you find the right plan for your organization.
            Get personalized recommendations and answers to your questions.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="btn btn-primary btn-lg">
              Contact sales
            </button>
            <button className="btn btn-outline btn-lg">
              View FAQ
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
