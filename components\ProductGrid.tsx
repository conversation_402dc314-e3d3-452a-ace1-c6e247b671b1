const products = [
  {
    name: 'Word',
    description: 'Create and edit beautiful documents with intelligent writing assistance',
    icon: (
      <svg className="w-10 h-10" fill="currentColor" viewBox="0 0 24 24">
        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
        <polyline points="14,2 14,8 20,8"/>
        <line x1="16" y1="13" x2="8" y2="13"/>
        <line x1="16" y1="17" x2="8" y2="17"/>
      </svg>
    ),
    bgColor: 'bg-blue-600',
    textColor: 'text-white',
    features: ['Real-time collaboration', 'Smart templates', 'Advanced formatting']
  },
  {
    name: 'Excel',
    description: 'Analyze data and create powerful visualizations with advanced formulas',
    icon: (
      <svg className="w-10 h-10" fill="currentColor" viewBox="0 0 24 24">
        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
        <line x1="9" y1="9" x2="15" y2="15"/>
        <line x1="15" y1="9" x2="9" y2="15"/>
      </svg>
    ),
    bgColor: 'bg-green-600',
    textColor: 'text-white',
    features: ['Dynamic charts', 'Pivot tables', 'Data analysis']
  },
  {
    name: 'PowerPoint',
    description: 'Create stunning presentations with designer templates and animations',
    icon: (
      <svg className="w-10 h-10" fill="currentColor" viewBox="0 0 24 24">
        <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
        <line x1="8" y1="21" x2="16" y2="21"/>
        <line x1="12" y1="17" x2="12" y2="21"/>
      </svg>
    ),
    bgColor: 'bg-orange-600',
    textColor: 'text-white',
    features: ['Designer templates', 'Presenter coach', 'Live captions']
  },
  {
    name: 'Outlook',
    description: 'Stay connected with intelligent email and calendar management',
    icon: (
      <svg className="w-10 h-10" fill="currentColor" viewBox="0 0 24 24">
        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
        <polyline points="22,6 12,13 2,6"/>
      </svg>
    ),
    bgColor: 'bg-blue-700',
    textColor: 'text-white',
    features: ['Focused inbox', 'Calendar integration', 'Meeting scheduler']
  },
  {
    name: 'OneNote',
    description: 'Capture ideas and organize notes across all your devices',
    icon: (
      <svg className="w-10 h-10" fill="currentColor" viewBox="0 0 24 24">
        <path d="M4 19.5A2.5 2.5 0 0 1 1.5 17V7A2.5 2.5 0 0 1 4 4.5h16A2.5 2.5 0 0 1 22.5 7v10a2.5 2.5 0 0 1-2.5 2.5H4z"/>
        <polyline points="9,11 12,14 22,4"/>
      </svg>
    ),
    bgColor: 'bg-purple-600',
    textColor: 'text-white',
    features: ['Cross-device sync', 'Handwriting support', 'Smart search']
  },
  {
    name: 'Access',
    description: 'Build custom database applications with easy-to-use tools',
    icon: (
      <svg className="w-10 h-10" fill="currentColor" viewBox="0 0 24 24">
        <ellipse cx="12" cy="5" rx="9" ry="3"/>
        <path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"/>
        <path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"/>
      </svg>
    ),
    bgColor: 'bg-red-600',
    textColor: 'text-white',
    features: ['Visual query builder', 'Form designer', 'Report generator']
  },
  {
    name: 'Publisher',
    description: 'Create professional publications and marketing materials',
    icon: (
      <svg className="w-10 h-10" fill="currentColor" viewBox="0 0 24 24">
        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
        <rect x="7" y="7" width="3" height="9"/>
        <rect x="14" y="7" width="3" height="5"/>
      </svg>
    ),
    bgColor: 'bg-teal-600',
    textColor: 'text-white',
    features: ['Layout templates', 'Typography tools', 'Print optimization']
  },
  {
    name: 'Teams',
    description: 'Collaborate with chat, meetings, and file sharing in one place',
    icon: (
      <svg className="w-10 h-10" fill="currentColor" viewBox="0 0 24 24">
        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
        <circle cx="9" cy="7" r="4"/>
        <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
        <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
      </svg>
    ),
    bgColor: 'bg-indigo-600',
    textColor: 'text-white',
    features: ['Video meetings', 'File collaboration', 'Chat integration']
  }
];

export default function ProductGrid() {
  return (
    <section id="products" className="py-16 lg:py-24 bg-surface">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-light text-foreground mb-6">
            Choose your <span className="font-semibold">Appfice</span>
          </h2>
          <p className="text-lg text-muted max-w-3xl mx-auto">
            The right app for every task. Work seamlessly across all your devices with apps that help you create, communicate, and collaborate.
          </p>
        </div>

        {/* Product Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {products.map((product, index) => (
            <div
              key={product.name}
              className="bg-card rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 group cursor-pointer overflow-hidden"
            >
              {/* Product Header */}
              <div className={`${product.bgColor} ${product.textColor} p-6 relative`}>
                <div className="flex items-center justify-between mb-4">
                  <div className="group-hover:scale-110 transition-transform duration-200">
                    {product.icon}
                  </div>
                  <svg className="w-5 h-5 opacity-70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-2">
                  {product.name}
                </h3>
                <p className="text-sm opacity-90 leading-relaxed">
                  {product.description}
                </p>
              </div>

              {/* Product Features */}
              <div className="p-6">
                <ul className="space-y-2">
                  {product.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-sm text-muted">
                      <svg className="w-4 h-4 text-primary mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      {feature}
                    </li>
                  ))}
                </ul>

                <div className="mt-6 pt-4 border-t border-border">
                  <button className="text-primary text-sm font-medium hover:underline">
                    Learn more about {product.name}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA Section */}
        <div className="bg-card rounded-lg shadow-lg p-8 lg:p-12 text-center">
          <h3 className="text-2xl lg:text-3xl font-light text-foreground mb-4">
            Get started with <span className="font-semibold">Appfice</span> today
          </h3>
          <p className="text-muted mb-8 max-w-2xl mx-auto">
            Try Appfice free for one month and see how it can transform the way you work.
            No credit card required.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="btn btn-primary btn-xl">
              Try free for 1 month
            </button>
            <button className="btn btn-outline btn-xl">
              Buy now
            </button>
          </div>
          <p className="text-xs text-muted mt-4">
            Or <a href="#" className="text-primary hover:underline">sign in</a> to get started
          </p>
        </div>
      </div>
    </section>
  );
}
