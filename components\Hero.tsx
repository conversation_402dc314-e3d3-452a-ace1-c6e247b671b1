export default function Hero() {
  return (
    <section className="relative bg-surface">
      {/* Main Hero Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-12 lg:pt-32 lg:pb-20">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="text-left">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-light text-foreground mb-6 leading-tight">
              Get more done with
              <span className="block font-semibold text-primary">Appfice</span>
            </h1>

            <p className="text-lg text-muted mb-8 leading-relaxed max-w-lg">
              The complete productivity suite that brings together everything you need to create,
              collaborate, and achieve more. Available anywhere, on any device.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 mb-8">
              <button className="btn btn-primary btn-xl">
                Try free for 1 month
              </button>
              <button className="btn btn-outline btn-xl">
                Buy now
              </button>
            </div>

            {/* Quick Links */}
            <div className="flex flex-wrap gap-6 text-sm">
              <a href="#" className="text-primary hover:underline">Or sign in</a>
              <a href="#" className="text-muted hover:text-foreground">Learn more</a>
            </div>
          </div>

          {/* Right Content - Product Showcase */}
          <div className="relative">
            <div className="bg-card rounded-lg shadow-xl p-8">
              <div className="grid grid-cols-2 gap-4 mb-6">
                {/* App Icons */}
                <div className="flex items-center space-x-3 p-3 rounded hover:bg-surface transition-colors cursor-pointer">
                  <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-foreground">Word</span>
                </div>

                <div className="flex items-center space-x-3 p-3 rounded hover:bg-surface transition-colors cursor-pointer">
                  <div className="w-8 h-8 bg-green-600 rounded flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                      <line x1="9" y1="9" x2="15" y2="15"/>
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-foreground">Excel</span>
                </div>

                <div className="flex items-center space-x-3 p-3 rounded hover:bg-surface transition-colors cursor-pointer">
                  <div className="w-8 h-8 bg-orange-600 rounded flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-foreground">PowerPoint</span>
                </div>

                <div className="flex items-center space-x-3 p-3 rounded hover:bg-surface transition-colors cursor-pointer">
                  <div className="w-8 h-8 bg-blue-700 rounded flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-foreground">Outlook</span>
                </div>
              </div>

              <div className="border-t border-border pt-4">
                <p className="text-xs text-muted text-center">
                  Access all your apps from anywhere
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Feature Highlights */}
      <div className="bg-card border-t border-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2">Enterprise security</h3>
              <p className="text-muted text-sm">Advanced security and compliance features to protect your data</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2">Real-time collaboration</h3>
              <p className="text-muted text-sm">Work together seamlessly with your team from anywhere</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2">Cloud-powered</h3>
              <p className="text-muted text-sm">Access your files and apps from any device, anywhere</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
