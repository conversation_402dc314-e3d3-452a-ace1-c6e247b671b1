const products = [
  {
    name: 'Word',
    description: 'Professional document creation and editing with advanced collaboration features',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
        <polyline points="14,2 14,8 20,8"/>
        <line x1="16" y1="13" x2="8" y2="13"/>
        <line x1="16" y1="17" x2="8" y2="17"/>
        <polyline points="10,9 9,9 8,9"/>
      </svg>
    ),
    color: 'text-blue-600'
  },
  {
    name: 'Excel',
    description: 'Advanced spreadsheet analysis with powerful data visualization and automation',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
        <line x1="9" y1="9" x2="15" y2="15"/>
        <line x1="15" y1="9" x2="9" y2="15"/>
      </svg>
    ),
    color: 'text-green-600'
  },
  {
    name: 'PowerPoint',
    description: 'Dynamic presentations with intelligent design suggestions and real-time collaboration',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
        <line x1="8" y1="21" x2="16" y2="21"/>
        <line x1="12" y1="17" x2="12" y2="21"/>
      </svg>
    ),
    color: 'text-orange-600'
  },
  {
    name: 'Outlook',
    description: 'Intelligent email management with integrated calendar and task scheduling',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
        <polyline points="22,6 12,13 2,6"/>
      </svg>
    ),
    color: 'text-blue-700'
  },
  {
    name: 'OneNote',
    description: 'Digital notebooks with cross-platform synchronization and intelligent search',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M4 19.5A2.5 2.5 0 0 1 1.5 17V7A2.5 2.5 0 0 1 4 4.5h16A2.5 2.5 0 0 1 22.5 7v10a2.5 2.5 0 0 1-2.5 2.5H4z"/>
        <polyline points="9,11 12,14 22,4"/>
      </svg>
    ),
    color: 'text-purple-600'
  },
  {
    name: 'Access',
    description: 'Database management with intuitive design tools and powerful query capabilities',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <ellipse cx="12" cy="5" rx="9" ry="3"/>
        <path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"/>
        <path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"/>
      </svg>
    ),
    color: 'text-red-600'
  },
  {
    name: 'Publisher',
    description: 'Professional publishing with advanced layout tools and design templates',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
        <rect x="7" y="7" width="3" height="9"/>
        <rect x="14" y="7" width="3" height="5"/>
      </svg>
    ),
    color: 'text-teal-600'
  },
  {
    name: 'Teams',
    description: 'Unified communication platform with video conferencing and file collaboration',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
        <circle cx="9" cy="7" r="4"/>
        <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
        <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
      </svg>
    ),
    color: 'text-indigo-600'
  }
];

export default function ProductGrid() {
  return (
    <section id="products" className="py-16 lg:py-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            Complete productivity suite
          </h2>
          <p className="text-lg text-muted max-w-3xl mx-auto">
            Eight powerful applications designed to work seamlessly together,
            providing everything your organization needs to succeed.
          </p>
        </div>

        {/* Product Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {products.map((product, index) => (
            <div
              key={product.name}
              className="card p-6 group cursor-pointer"
              style={{ animationDelay: `${index * 0.05}s` }}
            >
              {/* Product Icon */}
              <div className={`w-12 h-12 ${product.color} mb-4 group-hover:scale-110 transition-transform duration-200`}>
                {product.icon}
              </div>

              {/* Product Info */}
              <h3 className="text-lg font-semibold text-foreground mb-2">
                {product.name}
              </h3>
              <p className="text-muted text-sm leading-relaxed">
                {product.description}
              </p>

              {/* Learn More Link */}
              <div className="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <button className="text-primary text-sm font-medium hover:underline">
                  Learn more →
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Integration Message */}
        <div className="text-center">
          <div className="card p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-foreground mb-4">
              Seamlessly integrated ecosystem
            </h3>
            <p className="text-muted mb-6 max-w-2xl mx-auto">
              All applications share a unified interface, common data formats, and real-time collaboration features.
              Work flows naturally between applications without friction.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn btn-primary">
                Start free trial
              </button>
              <button className="btn btn-secondary">
                View all features
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
