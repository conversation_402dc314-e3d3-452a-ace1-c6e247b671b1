'use client';

import { useState, useEffect } from 'react';

const testimonials = [
  {
    name: '<PERSON>',
    role: 'Product Manager',
    company: 'TechFlow Inc.',
    avatar: '👩‍💼',
    content: 'Appfice has completely transformed how our team collaborates. The AI-powered features save us hours every week, and the seamless integration between all applications is incredible.',
    rating: 5
  },
  {
    name: '<PERSON>',
    role: 'Creative Director',
    company: 'Design Studio Pro',
    avatar: '👨‍🎨',
    content: 'The design tools in Appfice are outstanding. PowerPoint and Publisher have never been this intuitive. Our presentation quality has improved dramatically.',
    rating: 5
  },
  {
    name: '<PERSON>',
    role: 'Data Analyst',
    company: 'Analytics Corp',
    avatar: '👩‍💻',
    content: 'Excel in Appfice is a game-changer. The advanced analytics and AI insights help me discover patterns I would have missed otherwise. Absolutely love it!',
    rating: 5
  },
  {
    name: '<PERSON>',
    role: 'CEO',
    company: 'StartupXYZ',
    avatar: '👨‍💼',
    content: 'As a startup, we needed powerful tools without the enterprise price tag. Appfice delivers everything we need at a fraction of the cost of traditional solutions.',
    rating: 5
  },
  {
    name: '<PERSON>',
    role: 'Project Manager',
    company: 'Global Solutions',
    avatar: '👩‍🔬',
    content: 'The collaboration features are phenomenal. Our distributed team feels more connected than ever. Teams integration makes communication effortless.',
    rating: 5
  },
  {
    name: '<PERSON> <PERSON>',
    role: 'Marketing Director',
    company: 'Brand Builders',
    avatar: '👨‍🚀',
    content: 'Appfice has streamlined our entire marketing workflow. From content creation to data analysis, everything works together beautifully.',
    rating: 5
  }
];

export default function Testimonials() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    setIsAutoPlaying(false);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
    setIsAutoPlaying(false);
  };

  const goToTestimonial = (index: number) => {
    setCurrentIndex(index);
    setIsAutoPlaying(false);
  };

  return (
    <section className="py-24 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-0 left-1/3 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/3 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl sm:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Loved by Teams Worldwide
            </span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Join thousands of satisfied customers who have transformed their productivity with Appfice
          </p>
        </div>

        {/* Main Testimonial Display */}
        <div className="relative max-w-4xl mx-auto mb-12">
          <div className="glass-strong rounded-3xl p-8 md:p-12 text-center">
            {/* Quote Icon */}
            <div className="text-6xl text-blue-500/20 mb-6">❝</div>
            
            {/* Testimonial Content */}
            <blockquote className="text-xl md:text-2xl text-gray-700 dark:text-gray-300 leading-relaxed mb-8 font-medium">
              "{testimonials[currentIndex].content}"
            </blockquote>

            {/* Rating */}
            <div className="flex justify-center mb-6">
              {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                <svg key={i} className="w-6 h-6 text-yellow-400 fill-current" viewBox="0 0 20 20">
                  <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
                </svg>
              ))}
            </div>

            {/* Author Info */}
            <div className="flex items-center justify-center space-x-4">
              <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-2xl">
                {testimonials[currentIndex].avatar}
              </div>
              <div className="text-left">
                <div className="font-semibold text-foreground text-lg">
                  {testimonials[currentIndex].name}
                </div>
                <div className="text-gray-600 dark:text-gray-400">
                  {testimonials[currentIndex].role}
                </div>
                <div className="text-blue-600 text-sm">
                  {testimonials[currentIndex].company}
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Arrows */}
          <button
            onClick={prevTestimonial}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 glass rounded-full flex items-center justify-center hover:glass-strong transition-all duration-200 hover:scale-110"
          >
            <svg className="w-6 h-6 text-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          
          <button
            onClick={nextTestimonial}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 glass rounded-full flex items-center justify-center hover:glass-strong transition-all duration-200 hover:scale-110"
          >
            <svg className="w-6 h-6 text-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>

        {/* Testimonial Dots */}
        <div className="flex justify-center space-x-3 mb-12">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => goToTestimonial(index)}
              className={`w-3 h-3 rounded-full transition-all duration-200 ${
                index === currentIndex
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 scale-125'
                  : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
              }`}
            />
          ))}
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
          {[
            { number: '4.9/5', label: 'Average Rating' },
            { number: '10,000+', label: 'Happy Customers' },
            { number: '99%', label: 'Would Recommend' },
            { number: '24/7', label: 'Customer Support' }
          ].map((stat, index) => (
            <div key={stat.label} className="text-center">
              <div className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
                {stat.number}
              </div>
              <div className="text-gray-600 dark:text-gray-400 text-sm">
                {stat.label}
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="glass-strong rounded-2xl p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Join Our Satisfied Customers
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Experience the difference Appfice can make for your team
            </p>
            <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-full font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg">
              Start Your Free Trial
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
