export default function Footer() {
  const currentYear = new Date().getFullYear();

  const footerSections = [
    {
      title: 'Products',
      links: [
        { name: 'Word', href: '#' },
        { name: 'Excel', href: '#' },
        { name: 'PowerPoint', href: '#' },
        { name: 'Outlook', href: '#' },
        { name: 'OneNote', href: '#' },
        { name: 'Access', href: '#' },
        { name: 'Publisher', href: '#' },
        { name: 'Teams', href: '#' }
      ]
    },
    {
      title: 'Solutions',
      links: [
        { name: 'Business', href: '#' },
        { name: 'Enterprise', href: '#' },
        { name: 'Education', href: '#' },
        { name: 'Government', href: '#' }
      ]
    },
    {
      title: 'Resources',
      links: [
        { name: 'Documentation', href: '#' },
        { name: 'API Reference', href: '#' },
        { name: 'Help Center', href: '#' },
        { name: 'Community', href: '#' },
        { name: 'Blog', href: '#' }
      ]
    },
    {
      title: 'Company',
      links: [
        { name: 'About', href: '#' },
        { name: 'Careers', href: '#' },
        { name: 'Press', href: '#' },
        { name: 'Contact', href: '#' }
      ]
    }
  ];

  return (
    <footer className="bg-secondary/30 border-t border-border pt-16 pb-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-8 mb-12">
          {/* Brand Section */}
          <div className="col-span-2 md:col-span-4 lg:col-span-1">
            <div className="mb-6">
              <h3 className="text-2xl font-bold text-primary mb-4">
                Appfice
              </h3>
              <p className="text-muted text-sm leading-relaxed">
                The complete office suite for modern organizations. Secure, reliable, and built for scale.
              </p>
            </div>
          </div>

          {/* Footer Links */}
          {footerSections.map((section) => (
            <div key={section.title} className="col-span-1">
              <h4 className="font-semibold text-foreground mb-4 text-sm">
                {section.title}
              </h4>
              <ul className="space-y-3">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="text-muted hover:text-primary transition-colors duration-200 text-sm"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-border pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-muted text-sm">
              © {currentYear} Appfice. All rights reserved.
            </div>

            <div className="flex flex-wrap justify-center md:justify-end space-x-6 text-sm">
              <a href="#" className="text-muted hover:text-primary transition-colors duration-200">
                Privacy
              </a>
              <a href="#" className="text-muted hover:text-primary transition-colors duration-200">
                Terms
              </a>
              <a href="#" className="text-muted hover:text-primary transition-colors duration-200">
                Security
              </a>
              <a href="#" className="text-muted hover:text-primary transition-colors duration-200">
                Status
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
