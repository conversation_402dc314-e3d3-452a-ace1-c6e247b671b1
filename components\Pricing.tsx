'use client';

import { useState } from 'react';

const plans = [
  {
    name: 'Business',
    price: { monthly: 12, yearly: 120 },
    description: 'For growing teams and small businesses',
    features: [
      'All productivity applications',
      '1TB cloud storage per user',
      'Real-time collaboration',
      'Email and chat support',
      'Mobile and desktop apps',
      'Basic admin controls'
    ],
    popular: false
  },
  {
    name: 'Enterprise',
    price: { monthly: 22, yearly: 220 },
    description: 'For large organizations with advanced needs',
    features: [
      'Everything in Business',
      'Unlimited cloud storage',
      'Advanced security controls',
      'SSO and directory integration',
      'Priority support',
      'Advanced analytics',
      'Custom integrations',
      'Compliance tools'
    ],
    popular: true
  },
  {
    name: 'Enterprise Plus',
    price: { monthly: 35, yearly: 350 },
    description: 'For mission-critical deployments',
    features: [
      'Everything in Enterprise',
      'Dedicated customer success manager',
      '99.9% uptime SLA',
      'Advanced threat protection',
      'Custom development support',
      'On-premises deployment options',
      'White-label solutions',
      '24/7 phone support'
    ],
    popular: false
  }
];

export default function Pricing() {
  const [isYearly, setIsYearly] = useState(false);

  return (
    <section id="pricing" className="py-16 lg:py-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            Simple, transparent pricing
          </h2>
          <p className="text-lg text-muted max-w-3xl mx-auto mb-8">
            Choose the plan that fits your organization's needs. All plans include a 30-day free trial.
          </p>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center space-x-4">
            <span className={`text-sm font-medium ${!isYearly ? 'text-foreground' : 'text-muted'}`}>
              Monthly
            </span>
            <button
              onClick={() => setIsYearly(!isYearly)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ${
                isYearly ? 'bg-primary' : 'bg-border'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ${
                  isYearly ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`text-sm font-medium ${isYearly ? 'text-foreground' : 'text-muted'}`}>
              Yearly
              <span className="ml-1 text-xs bg-accent/20 text-accent px-2 py-1 rounded-full">
                Save 17%
              </span>
            </span>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto mb-16">
          {plans.map((plan, index) => (
            <div
              key={plan.name}
              className={`card p-8 relative ${
                plan.popular ? 'ring-2 ring-primary scale-105' : ''
              }`}
              style={{ animationDelay: `${index * 0.05}s` }}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <div className="bg-primary text-white px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </div>
                </div>
              )}

              {/* Plan Header */}
              <div className="text-center mb-8">
                <h3 className="text-xl font-bold text-foreground mb-2">
                  {plan.name}
                </h3>
                <p className="text-muted text-sm mb-6">
                  {plan.description}
                </p>

                {/* Price */}
                <div className="mb-6">
                  <div className="flex items-baseline justify-center">
                    <span className="text-4xl font-bold text-foreground">
                      ${isYearly ? plan.price.yearly : plan.price.monthly}
                    </span>
                    <span className="text-muted ml-2">
                      /{isYearly ? 'year' : 'month'}
                    </span>
                  </div>
                  {isYearly && (
                    <div className="text-sm text-muted mt-1">
                      ${(plan.price.yearly / 12).toFixed(0)}/month billed annually
                    </div>
                  )}
                </div>
              </div>

              {/* Features */}
              <ul className="space-y-3 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <li key={feature} className="flex items-start">
                    <div className="w-5 h-5 bg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span className="ml-3 text-muted text-sm">
                      {feature}
                    </span>
                  </li>
                ))}
              </ul>

              {/* CTA Button */}
              <button className={`w-full py-3 px-6 rounded-lg font-medium transition-all duration-200 ${
                plan.popular
                  ? 'btn btn-primary'
                  : 'btn btn-secondary'
              }`}>
                {plan.name === 'Enterprise Plus' ? 'Contact Sales' : 'Start Free Trial'}
              </button>
            </div>
          ))}
        </div>

        {/* Bottom Section */}
        <div className="text-center">
          <div className="card p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-foreground mb-4">
              Need a custom solution?
            </h3>
            <p className="text-muted mb-6">
              Our enterprise team can work with you to create a tailored solution for your organization's unique requirements.
            </p>
            <button className="btn btn-primary">
              Contact enterprise sales
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
