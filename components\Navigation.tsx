'use client';

import { useState, useEffect } from 'react';

export default function Navigation() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-200 ${
      isScrolled ? 'bg-card/95 backdrop-blur-md border-b border-border shadow-sm' : 'bg-card'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-14">
          {/* Logo */}
          <div className="flex items-center space-x-8">
            <div className="flex-shrink-0">
              <h1 className="text-xl font-semibold text-foreground">
                Appfice
              </h1>
            </div>

            {/* Navigation Links */}
            <div className="hidden lg:flex items-center space-x-6">
              <div className="relative group">
                <button className="text-foreground hover:text-primary transition-colors duration-200 text-sm font-medium py-2 flex items-center">
                  Products
                  <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
              </div>

              <div className="relative group">
                <button className="text-foreground hover:text-primary transition-colors duration-200 text-sm font-medium py-2 flex items-center">
                  Solutions
                  <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
              </div>

              <a href="#pricing" className="text-foreground hover:text-primary transition-colors duration-200 text-sm font-medium py-2">
                Plans and pricing
              </a>

              <div className="relative group">
                <button className="text-foreground hover:text-primary transition-colors duration-200 text-sm font-medium py-2 flex items-center">
                  Resources
                  <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          {/* Right Side */}
          <div className="flex items-center space-x-4">
            {/* Search */}
            <button className="hidden md:flex items-center text-muted hover:text-foreground transition-colors duration-200">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button>

            {/* Account */}
            <div className="hidden md:flex items-center space-x-2">
              <button className="text-foreground hover:text-primary transition-colors duration-200 text-sm font-medium px-3 py-2">
                Sign in
              </button>
              <button className="btn btn-primary">
                Try free
              </button>
            </div>

            {/* Mobile menu button */}
            <div className="lg:hidden">
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="text-foreground hover:text-primary transition-colors duration-200 p-2"
              >
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  {isMobileMenuOpen ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  )}
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Mobile menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden border-t border-border bg-card">
            <div className="px-4 py-3 space-y-2">
              <a href="#products" className="block px-3 py-2 text-foreground hover:text-primary transition-colors duration-200 text-sm font-medium">
                Products
              </a>
              <a href="#solutions" className="block px-3 py-2 text-foreground hover:text-primary transition-colors duration-200 text-sm font-medium">
                Solutions
              </a>
              <a href="#pricing" className="block px-3 py-2 text-foreground hover:text-primary transition-colors duration-200 text-sm font-medium">
                Plans and pricing
              </a>
              <a href="#resources" className="block px-3 py-2 text-foreground hover:text-primary transition-colors duration-200 text-sm font-medium">
                Resources
              </a>
              <div className="pt-3 border-t border-border mt-3">
                <button className="block w-full text-left px-3 py-2 text-foreground hover:text-primary transition-colors duration-200 text-sm font-medium mb-2">
                  Sign in
                </button>
                <button className="btn btn-primary w-full">
                  Try free
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
