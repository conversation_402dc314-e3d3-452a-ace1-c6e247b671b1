'use client';

import { useState, useEffect, useRef } from 'react';

const features = [
  {
    title: 'AI-Powered Intelligence',
    description: 'Advanced AI assistance across all applications to enhance your productivity and creativity.',
    icon: '🤖',
    details: [
      'Smart content suggestions',
      'Automated formatting',
      'Intelligent data analysis',
      'Natural language processing'
    ]
  },
  {
    title: 'Real-Time Collaboration',
    description: 'Work together seamlessly with your team, no matter where you are in the world.',
    icon: '🤝',
    details: [
      'Live co-editing',
      'Comment and review system',
      'Version history tracking',
      'Instant notifications'
    ]
  },
  {
    title: 'Cloud-First Architecture',
    description: 'Access your work from anywhere with automatic sync and backup across all devices.',
    icon: '☁️',
    details: [
      'Cross-platform compatibility',
      'Automatic cloud backup',
      'Offline mode support',
      'Instant sync across devices'
    ]
  },
  {
    title: 'Enterprise Security',
    description: 'Bank-level security with advanced encryption and compliance standards.',
    icon: '🔒',
    details: [
      'End-to-end encryption',
      'Multi-factor authentication',
      'GDPR & SOC 2 compliance',
      'Advanced threat protection'
    ]
  },
  {
    title: 'Seamless Integration',
    description: 'Connect with your favorite tools and services for a unified workflow experience.',
    icon: '🔗',
    details: [
      '1000+ app integrations',
      'API access',
      'Custom workflows',
      'Third-party plugins'
    ]
  },
  {
    title: 'Advanced Analytics',
    description: 'Gain insights into your productivity and team performance with detailed analytics.',
    icon: '📊',
    details: [
      'Usage analytics',
      'Performance metrics',
      'Team insights',
      'Custom reports'
    ]
  }
];

export default function Features() {
  const [visibleFeatures, setVisibleFeatures] = useState<number[]>([]);
  const [activeFeature, setActiveFeature] = useState<number | null>(null);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.getAttribute('data-index') || '0');
            setVisibleFeatures(prev => [...prev, index]);
          }
        });
      },
      { threshold: 0.1 }
    );

    const featureElements = sectionRef.current?.querySelectorAll('[data-index]');
    featureElements?.forEach(el => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  return (
    <section id="features" ref={sectionRef} className="py-24 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl sm:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Powerful Features
            </span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Discover the advanced capabilities that make Appfice the most comprehensive office suite
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div
              key={feature.title}
              data-index={index}
              className={`group relative glass rounded-2xl p-8 transition-all duration-700 hover:glass-strong cursor-pointer ${
                visibleFeatures.includes(index) 
                  ? 'opacity-100 translate-y-0' 
                  : 'opacity-0 translate-y-8'
              } ${
                activeFeature === index ? 'scale-105 z-10' : ''
              }`}
              style={{ transitionDelay: `${index * 0.1}s` }}
              onMouseEnter={() => setActiveFeature(index)}
              onMouseLeave={() => setActiveFeature(null)}
            >
              {/* Feature Icon */}
              <div className="w-16 h-16 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
                {feature.icon}
              </div>

              {/* Feature Content */}
              <h3 className="text-xl font-bold mb-4 text-foreground group-hover:text-blue-600 transition-colors duration-300">
                {feature.title}
              </h3>
              
              <p className="text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">
                {feature.description}
              </p>

              {/* Feature Details */}
              <div className={`space-y-3 transition-all duration-500 ${
                activeFeature === index 
                  ? 'opacity-100 max-h-48 translate-y-0' 
                  : 'opacity-0 max-h-0 translate-y-4 overflow-hidden'
              }`}>
                {feature.details.map((detail, detailIndex) => (
                  <div 
                    key={detail}
                    className="flex items-center text-sm text-gray-600 dark:text-gray-400"
                    style={{ 
                      transitionDelay: `${detailIndex * 0.05}s`,
                      transform: activeFeature === index ? 'translateX(0)' : 'translateX(-10px)'
                    }}
                  >
                    <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 mr-3 flex-shrink-0"></div>
                    {detail}
                  </div>
                ))}
              </div>

              {/* Hover Glow Effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-600/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>

              {/* Learn More Button */}
              <div className={`mt-6 transition-all duration-300 ${
                activeFeature === index 
                  ? 'opacity-100 translate-y-0' 
                  : 'opacity-0 translate-y-2'
              }`}>
                <button className="text-blue-600 hover:text-purple-600 font-medium text-sm flex items-center group-hover:translate-x-1 transition-transform duration-200">
                  Learn more
                  <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom Section */}
        <div className="mt-20 text-center">
          <div className="glass-strong rounded-3xl p-12 max-w-4xl mx-auto">
            <h3 className="text-3xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Experience the Future Today
            </h3>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
              Join millions of users who have already transformed their productivity with Appfice's cutting-edge features.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-full font-semibold text-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg">
                Start Free Trial
              </button>
              <button className="glass text-foreground px-8 py-4 rounded-full font-semibold text-lg hover:glass-strong transition-all duration-200 border border-white/20">
                Schedule Demo
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
