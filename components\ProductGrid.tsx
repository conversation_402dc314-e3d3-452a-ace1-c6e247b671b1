'use client';

import { useState } from 'react';

const products = [
  {
    name: 'Word',
    description: 'Advanced word processing with AI-powered writing assistance',
    icon: '📝',
    color: 'from-blue-500 to-blue-700',
    features: ['Smart Writing', 'Real-time Collaboration', 'Advanced Formatting', 'AI Grammar Check']
  },
  {
    name: 'Excel',
    description: 'Powerful spreadsheets with intelligent data analysis',
    icon: '📊',
    color: 'from-green-500 to-green-700',
    features: ['Dynamic Charts', 'Data Analysis', 'Pivot Tables', 'Formula Intelligence']
  },
  {
    name: 'PowerPoint',
    description: 'Create stunning presentations with smart design tools',
    icon: '📈',
    color: 'from-orange-500 to-red-600',
    features: ['Smart Templates', 'Animation Studio', 'Presenter Coach', 'Live Captions']
  },
  {
    name: 'Outlook',
    description: 'Intelligent email and calendar management',
    icon: '📧',
    color: 'from-blue-600 to-indigo-700',
    features: ['Smart Inbox', 'Calendar Integration', 'Meeting Scheduler', 'Email Analytics']
  },
  {
    name: 'OneNote',
    description: 'Digital notebook for capturing and organizing ideas',
    icon: '📔',
    color: 'from-purple-500 to-purple-700',
    features: ['Cross-platform Sync', 'Handwriting Recognition', 'Audio Notes', 'Smart Search']
  },
  {
    name: 'Access',
    description: 'Database management made simple and powerful',
    icon: '🗃️',
    color: 'from-red-500 to-pink-600',
    features: ['Visual Query Builder', 'Form Designer', 'Report Generator', 'Data Import/Export']
  },
  {
    name: 'Publisher',
    description: 'Professional desktop publishing and design',
    icon: '🎨',
    color: 'from-teal-500 to-cyan-600',
    features: ['Layout Templates', 'Typography Tools', 'Image Editing', 'Print Optimization']
  },
  {
    name: 'Teams',
    description: 'Seamless collaboration and communication platform',
    icon: '👥',
    color: 'from-indigo-500 to-purple-600',
    features: ['Video Conferencing', 'File Sharing', 'Chat Integration', 'Workflow Automation']
  }
];

export default function ProductGrid() {
  const [hoveredProduct, setHoveredProduct] = useState<string | null>(null);

  return (
    <section id="products" className="py-24 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl sm:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Complete Office Suite
            </span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Everything you need for modern productivity, all in one integrated platform
          </p>
        </div>

        {/* Product Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {products.map((product, index) => (
            <div
              key={product.name}
              className={`group relative glass rounded-2xl p-6 transition-all duration-500 hover:scale-105 hover:glass-strong cursor-pointer ${
                hoveredProduct === product.name ? 'z-10' : ''
              }`}
              onMouseEnter={() => setHoveredProduct(product.name)}
              onMouseLeave={() => setHoveredProduct(null)}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {/* Product Icon */}
              <div className={`w-16 h-16 rounded-xl bg-gradient-to-br ${product.color} flex items-center justify-center text-2xl mb-4 group-hover:scale-110 transition-transform duration-300`}>
                {product.icon}
              </div>

              {/* Product Info */}
              <h3 className="text-xl font-bold mb-2 text-foreground">
                {product.name}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 leading-relaxed">
                {product.description}
              </p>

              {/* Features List */}
              <div className={`space-y-2 transition-all duration-300 ${
                hoveredProduct === product.name ? 'opacity-100 max-h-40' : 'opacity-0 max-h-0 overflow-hidden'
              }`}>
                {product.features.map((feature, featureIndex) => (
                  <div 
                    key={feature}
                    className="flex items-center text-sm text-gray-600 dark:text-gray-400"
                    style={{ animationDelay: `${featureIndex * 0.05}s` }}
                  >
                    <div className={`w-2 h-2 rounded-full bg-gradient-to-r ${product.color} mr-2 flex-shrink-0`}></div>
                    {feature}
                  </div>
                ))}
              </div>

              {/* Hover Effect Overlay */}
              <div className={`absolute inset-0 bg-gradient-to-br ${product.color} opacity-0 group-hover:opacity-5 rounded-2xl transition-opacity duration-300`}></div>

              {/* Launch Button */}
              <div className={`mt-4 transition-all duration-300 ${
                hoveredProduct === product.name ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'
              }`}>
                <button className={`w-full bg-gradient-to-r ${product.color} text-white py-2 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-200 transform hover:scale-105`}>
                  Launch {product.name}
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="glass-strong rounded-2xl p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Ready to boost your productivity?
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Get access to all applications with a single subscription
            </p>
            <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-full font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg">
              Start Your Free Trial
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
